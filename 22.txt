stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Documents/ifkc.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Documents/akc.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/faker.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/src.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/selectApp.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/config.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/ifaddrs.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/deep.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/addressInfo.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/faker.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/src.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/selectApp.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/config.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/ifaddrs.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/deep.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/multiSelect.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/aa.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/addressInfo.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Media/AMG/export
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/faker.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/src.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/selectApp.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/config.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/ifaddrs.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/deep.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/multiSelect.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/aa.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/addressInfo.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/AMG/原始机器
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.lsd.mapdb
stringWithFormat= __NSXPCInterfaceProxy__LSDReadProtocol
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.lsd.icons
stringWithFormat= __NSXPCInterfaceProxy__LSDIconServiceProtocol
stringWithFormat= BundleID:com.tigisoftware
stringWithFormat= BundleID:com.roothide
stringWithFormat= BundleID:org.coolstar
stringWithFormat= BundleID:com.roothide
stringWithFormat= BundleID:com.opa334
stringWithFormat= BundleID:nbtd.vip
stringWithFormat= BundleID:com.netskao
stringWithFormat= BundleID:cn.gblw
stringWithFormat= BundleID:com.opa334
stringWithFormat= BundleID:wiki.qaq
stringWithFormat= BundleID:com.superdev
stringWithFormat= BundleID:xyz.yyyue
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/AMG
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/sibrary/MobileSubstrate/DynamicLibraries/SubstrateSafety.dylib
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/sibrary/MobileSubstrate/DynamicLibraries/SubstrateSafety.plist
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-GsO-MK-tmh.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-GsO-MK-tmh.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-WH0-HE-pli.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-WH0-HE-pli.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/lDk-Dn-MeJ-view-teD-f2-rDG.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/lDk-Dn-MeJ-view-teD-f2-rDG.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-l4S-UR-W3L.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-l4S-UR-W3L.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/WH0-HE-pli-view-pGQ-Uj-O3Y.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/WH0-HE-pli-view-pGQ-Uj-O3Y.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/backupList.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/backupList.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-jrD-Xg-c9j.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-jrD-Xg-c9j.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/B4h-un-IhN-view-BPj-K1-qU2.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/B4h-un-IhN-view-BPj-K1-qU2.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-oSi-1F-cap.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-oSi-1F-cap.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/P9P-kj-IWt-view-SlX-mT-u79.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/P9P-kj-IWt-view-SlX-mT-u79.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/oSi-1F-cap-view-asn-aw-mLW.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/oSi-1F-cap-view-asn-aw-mLW.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-6Va-U1-tRD.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-6Va-U1-tRD.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/Nyk-ex-WgO-view-tSd-y4-Sv7.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/Nyk-ex-WgO-view-tSd-y4-Sv7.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UINavigationController-peh-uj-LUi.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UINavigationController-peh-uj-LUi.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-P9P-kj-IWt.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UIViewController-P9P-kj-IWt.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/GsO-MK-tmh-view-8oK-8D-M8D.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/GsO-MK-tmh-view-8oK-8D-M8D.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/W1h-ZW-aQn-view-Pme-eq-b7s.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/W1h-ZW-aQn-view-Pme-eq-b7s.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-B4h-un-IhN.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-B4h-un-IhN.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/zDg-re-XMD-view-7vF-qn-HBB.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/zDg-re-XMD-view-7vF-qn-HBB.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/Tte-xW-HSE-view-JXM-oM-VIv.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/Tte-xW-HSE-view-JXM-oM-VIv.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-UP6-kI-QdY.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-UP6-kI-QdY.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/e5a-NJ-LJW-view-okq-AK-hmv.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/e5a-NJ-LJW-view-okq-AK-hmv.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/5Nb-nN-cd4-view-Ms8-ge-2B3.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/5Nb-nN-cd4-view-Ms8-ge-2B3.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/l4S-UR-W3L-view-5pp-CP-gez.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/l4S-UR-W3L-view-5pp-CP-gez.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/jrD-Xg-c9j-view-dsf-e7-T4F.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/jrD-Xg-c9j-view-dsf-e7-T4F.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/BkJ-G3-zfq-view-hc0-Kz-ywf.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/BkJ-G3-zfq-view-hc0-Kz-ywf.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-zDg-re-XMD.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-zDg-re-XMD.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/6Va-U1-tRD-view-n0l-8p-uUV.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/6Va-U1-tRD-view-n0l-8p-uUV.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-5Nb-nN-cd4.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-5Nb-nN-cd4.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-iG6-Qr-Mjl.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-iG6-Qr-Mjl.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-e5a-NJ-LJW.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-e5a-NJ-LJW.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UP6-kI-QdY-view-VgD-DG-oB2.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UP6-kI-QdY-view-VgD-DG-oB2.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/iG6-Qr-Mjl-view-YVZ-aw-Hcf.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/iG6-Qr-Mjl-view-YVZ-aw-Hcf.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-Tte-xW-HSE.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-Tte-xW-HSE.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/BackupDetail.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/BackupDetail.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-Nyk-ex-WgO.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-Nyk-ex-WgO.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-KsD-pt-MYe.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/UITableViewController-KsD-pt-MYe.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/KsD-pt-MYe-view-lhd-Or-WN7.nib/objects-11.0+.nib
stringWithFormat= /private/var/containers/Bundle/Application/.jbroot-2048042941566C3E/Applications/AMG.app/Base.lproj/Main.storyboardc/KsD-pt-MYe-view-lhd-Or-WN7.nib/objects-11.0+.nib
stringWithFormat= 1754146685
stringWithFormat= [C:1]
stringWithFormat= BSXPCCnx:com.apple.frontboard.systemappservices
stringWithFormat= [C:1-1]
stringWithFormat= 1
stringWithFormat= SpringBoard.81066
stringWithFormat= task_name_for_pid:SpringBoard.81066
stringWithFormat= (83519:3807:send task_name_for_pid:SpringBoard.81066)
stringWithFormat= 348-81066-32677
stringWithFormat= SpringBoard:81066
stringWithFormat= application<com.superdev.AMG>
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= 2
stringWithFormat= 1
stringWithFormat= [C:1-2]
stringWithFormat= 1
stringWithFormat= 2
stringWithFormat= BSXPCCnx:com.apple.frontboard.systemappservices (BSCnx:client:com.apple.uis.applicationSupportService)
stringWithFormat= BSXPCCnx:com.apple.frontboard.systemappservices (BSCnx:client:com.apple.frontboard.workspace-service)
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<UISApplicationSupportXPCServerInterface>
stringWithFormat= SpringBoard:81066
stringWithFormat= SpringBoard:81066
stringWithFormat=  from (81066:2cc33:send-once take)
stringWithFormat= (83519:5803:send-once xpcCode) from (81066:2cc33:send-once take)
stringWithFormat= AXIPCClient:<0x283b25950> Service:com.apple.accessibility.AXBackBoardServer ID:(null) connected:1
stringWithFormat= <_NSMainThread: 0x281c28400>{number = 1, name = main}
stringWithFormat= AXIPCClient:<0x283b25950> Service:com.apple.accessibility.AXBackBoardServer ID:com.apple.accessibility.AXSytemReplyServer-83519-0 connected:1
stringWithFormat= <_NSMainThread: 0x281c28400>{number = 1, name = main}
stringWithFormat= com.apple.dt.testmanagerd.availability.83519
stringWithFormat= com.apple.dt.xctestd.availability.83519
stringWithFormat= com.apple.dt.xctest.remote.availability.83519
stringWithFormat= NameLayerTree-83519
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Library/Saved Application State/com.superdev.AMG.savedState
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= EventDeferringState:sceneID:com.superdev.AMG-default
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Library/Saved Application State/com.superdev.AMG.savedState
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.UIKit.KeyboardManagement.hosted
stringWithFormat= __NSXPCInterfaceProxy__UIKeyboardArbitration
stringWithFormat= __NSXPCInterfaceProxy__UIKeyboardArbitrationClient
stringWithFormat= BackButtonGuide(0x10ff17920)
stringWithFormat= LeadingBarGuide(0x10ff17920)
stringWithFormat= TitleView(0x10ff17920)
stringWithFormat= TrailingBarGuide(0x10ff17920)
stringWithFormat= BackButtonGuide(0x10ff17920).minimumWidth
stringWithFormat= BackButtonGuide(0x10ff17920).maximumWidth
stringWithFormat= /var/mobile/Library/Fonts/AddedFontCache.plist
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.fontservicesd
stringWithFormat= __NSXPCInterfaceProxy_FontServicesDaemonProtocol
stringWithFormat= __NSXPCInterfaceProxy_FontServicesClientProtocol
stringWithFormat= UIWindow-0x10ff1a8d0-0
stringWithFormat= UIWindowScene: 0x10ff08e30: end key window deferring for window hiding: 0x10ff1a8d0
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= VC:UINavigationController
stringWithFormat= presentationController : <_UIRootPresentationController: 0x10fe0e760> presentedViewController : <UINavigationController: 0x110824400> presentingViewController : (null)
stringWithFormat= com.apple.menu.dynamic.8E4FCC5F-89B9-476E-959A-2AE750BA8C64
stringWithFormat= <UINavigationController:0x110824400> Backdrop Group
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= com.apple.menu.dynamic.4E879C3E-7647-4BC3-9100-E7C58F8511CF
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= UIWindowScene: 0x10ff08e30: Window requested to become key in scene: 0x10ff1a8d0
stringWithFormat= UIWindowScene: 0x10ff08e30: Begin event deferring in keyboardFocus for window: 0x10ff1a8d0
stringWithFormat= 1-deferDiscrete: UIWindowScene: 0x10ff08e30: Begin event deferring in keyboardFocus for window: 0x10ff1a8d0
stringWithFormat= com.apple.coreui-cache /System/Library/CoreServices/CoreGlyphs.bundle/Assets.car
stringWithFormat= com.apple.coreui-negativecache /System/Library/CoreServices/CoreGlyphs.bundle/Assets.car
stringWithFormat= com.apple.coreui-cuicache /System/Library/CoreServices/CoreGlyphs.bundle/Assets.car
stringWithFormat= interpolatedsymbol-chevron.forward-0{0-0-1-0-0-4-1-0-0-5-5-424c-55-3b-0-6-1
stringWithFormat= interpolatedsymbol-chevron.forward-0{0-0-1-0-0-4-1-0-0-5-5-424c-55-3b-0-6-1-2-14.0-1.0-0
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameUserInterfaceIdiom
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameDisplayScale
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameDisplayGamut
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameHorizontalSizeClass
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameVerticalSizeClass
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameTouchLevel
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameInteractionModel
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNamePrimaryInteractionModel
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameArtworkSubtype
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameUserInterfaceStyle
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameUserInterfaceLayoutDirection
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameForceTouchCapability
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNamePreferredContentSizeCategory
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameDisplayCornerRadius
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameLegibilityWeight
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameSemanticContext
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNamePresentationSemanticContext
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameSplitViewControllerContext
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameAccessibilityContrast
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameUserInterfaceLevel
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameVibrancy
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameActiveAppearance
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameFocusSystemState
stringWithFormat= UITraitCollectionBuiltinTrait-_UITraitNameSelectionFollowsFocus
stringWithFormat= VC:MainViewController
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/com.apple.networkservices.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/com.zorro.advfile.plist
stringWithFormat= 00008003-001C55A408BB0226
stringWithFormat= https://m.ejmk123.top/83/ddddesc_cert.php
stringWithFormat= 264
stringWithFormat= com.apple.CFNetwork.NSURLSession.{09345F95-F870-481F-AAB0-61F977A761F1}{(null)}{Y}{2}
stringWithFormat= Task <EFE55E58-5175-4F57-8E98-8AB7E18A88C1>.<1>
stringWithFormat= 83519
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Library/Caches/com.superdev.AMG/Cache.db
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Library/Caches/com.superdev.AMG/Cache.db-shm
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/root/Library/Caches/com.superdev.AMG/Cache.db-wal
stringWithFormat= DELETE FROM alt_services WHERE ROWID in (SELECT ROWID FROM alt_services ORDER BY ROWID DESC LIMIT -1 OFFSET 1500)
stringWithFormat= 348-81066-32683
stringWithFormat= 348-81066-32677
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/cert.crt
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/pri.key
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/cert.crt
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/pri.key
stringWithFormat= VC:_UIAlertControllerTextFieldViewController
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= groupView.actionsSequenceView
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= groupView.actionsSequenceView
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= <_UIAlertControllerInterfaceActionGroupView: 0x10ff54b60>
stringWithFormat= VC:UIAlertController
stringWithFormat= presentationController : <_UIAlertControllerAlertPresentationController: 0x10ff44db0> presentedViewController : <UIAlertController: 0x11088ba00> presentingViewController : <UINavigationController: 0x110824400>
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/AMG/backupConfig.plist
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/ip.txt
stringWithFormat= <UINavigationController:0x110824400> Backdrop Group
stringWithFormat= https://m.ejmk123.top/83/gip.php
stringWithFormat= 0
stringWithFormat= Task <8649DECD-E94D-4234-B9D8-BC6CD5CB1D9A>.<2>
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/ip.txt
stringWithFormat= IP: ***********
stringWithFormat= _UIToolbarContentView-UIBarButtonItem
stringWithFormat= _UIBarContentView-UIBarButtonItem
stringWithFormat= UIView-UIBarButtonItem
stringWithFormat= _UIButtonBarStackView:0x10fe45770
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.minimumInterItemSpace
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.minimumInterItemSpace:0x281c02380
stringWithFormat= _UIButtonBarStackView:0x10fe45770
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.minimumInterGroupSpace
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.minimumInterGroupSpace:0x281c02200
stringWithFormat= _UIButtonBarStackView:0x10fe45770
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.flexibleSpaceEqualSize
stringWithFormat= _UIButtonBarStackView:0x10fe45770.UIButtonBar.flexibleSpaceEqualSize:0x281c00880
stringWithFormat= http://**************:8080/
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/Library/MobileSubstrate/DynamicLibraries/amg.dylib
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.lsd.advertisingidentifiers
stringWithFormat= __NSXPCInterfaceProxy__LSDDeviceIdentifierProtocol
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/AMG_tar
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/cert.crt
stringWithFormat= /var/containers/Bundle/Application/.jbroot-2048042941566C3E/var/mobile/Library/Preferences/AMG/pri.key
stringWithFormat= com.apple.menu.dynamic.38719CFB-A5F7-4BB3-A5D5-2CD3D5BDF8C0
stringWithFormat= UIButtonBar.sizingGuide.0x282a204b0
stringWithFormat= com.apple.menu.dynamic.44973733-A3E9-49EE-ACF6-4DFFFC24407B
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= com.apple.menu.dynamic.FD9B8EEF-36CA-4255-B4C6-0D93B208A7EA
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= appLaunchSuspendedTime.com.superdev.AMG
stringWithFormat= appLaunchSuspendedTime.com.superdev.AMG.3.1
stringWithFormat= <_UIAlertControllerView 0x10ff54400>
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= 348-81066-32688
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.luminanceCurveMap.
stringWithFormat= filters.colorSaturate.
stringWithFormat= filters.colorBrightness.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= <_UIAlertControllerTextFieldViewCollectionCell: 0x10ff762d0; 
stringWithFormat= KeyboardAutocorrection_changedAt
stringWithFormat= KeyboardAutocorrection_analyzedAt
stringWithFormat= KeyboardAutocorrection_previousValue
stringWithFormat= KeyboardAutocorrection_buildAtChange
stringWithFormat= KeyboardAutocorrection_approxDateOfBuildInstall
stringWithFormat= KeyboardAutocorrection_buildAtLastAnalysis
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= com.apple.powerlog.state_changed.ProcessName.AMG
stringWithFormat= com.apple.powerlog.state_changed.ClientID.23
stringWithFormat= 0x280f4dfe0:transform
stringWithFormat= 0x280f4dfe0:uiFractionalProgress
stringWithFormat= 0x280f4dfe0:transform
stringWithFormat= 0x280f4dfe0:opacity
stringWithFormat= 0x280f4dfe0:opacity
stringWithFormat= 0x280f71e80:opacity
stringWithFormat= 0x280f71e80:opacity
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= 0x282a20e60:contents
stringWithFormat= 0x280f1ee80:opacity
stringWithFormat= 0x280f1ee80:uiFractionalProgress
stringWithFormat= UIVariableDelayLoupeGesture: 0x10fe5daa0
stringWithFormat= <UIVariableDelayLoupeGesture: 0x10fe5daa0 (UITextInteractionNameInteractiveLoupe)>
stringWithFormat= UITapAndAHalfRecognizer: 0x10fe5c2d0
stringWithFormat= <UITapAndAHalfRecognizer: 0x10fe5c2d0 (UITextInteractionNameTapAndAHalf)>
stringWithFormat= UITextMultiTapRecognizer: 0x10fe5dc80
stringWithFormat= <UITextMultiTapRecognizer: 0x10fe5dc80 (UITextInteractionNameSingleTap)>
stringWithFormat= VC:UISystemInputAssistantViewController
stringWithFormat= 0x280f79380:bounds.origin
stringWithFormat= 0x280f79380:bounds.origin
stringWithFormat= 0x280f79380:bounds
stringWithFormat= 0x280f79380:bounds.size
stringWithFormat= 0x280f79380:bounds.size
stringWithFormat= 0x280f79380:bounds
stringWithFormat= 0x280f793a0:position
stringWithFormat= 0x280f793a0:position
stringWithFormat= 0x280f793a0:position
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= KeyboardAutocorrection_changedAt
stringWithFormat= KeyboardAutocorrection_analyzedAt
stringWithFormat= KeyboardAutocorrection_previousValue
stringWithFormat= KeyboardAutocorrection_buildAtChange
stringWithFormat= KeyboardAutocorrection_approxDateOfBuildInstall
stringWithFormat= KeyboardAutocorrection_buildAtLastAnalysis
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= en_US
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= emoji
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= VC:UIPredictionViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= dictation
stringWithFormat= dictation
stringWithFormat= /System/Library/TextInput/TextInput_dictation.bundle
stringWithFormat= /System/Library/TextInput/TextInput_mul.bundle
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.TextInput
stringWithFormat= __NSXPCInterfaceProxy_TIKeyboardInputManager
stringWithFormat= __NSXPCInterfaceProxy_TIKeyboardInputManagerToImplProtocol
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= VC:UICompatibilityInputViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= VC:UICompatibilityInputViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UITextEffectsWindow-0x10fe66d30-1
stringWithFormat= UIWindowScene: 0x10ff08e30: end key window deferring for window hiding: 0x10fe66d30
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= VC:UIInputWindowController
stringWithFormat= 0x280f791e0:position
stringWithFormat= 0x280f791e0:position
stringWithFormat= 0x280f791e0:position
stringWithFormat= 0x280f791e0:bounds.origin
stringWithFormat= 0x280f791e0:bounds.origin
stringWithFormat= 0x280f791e0:bounds
stringWithFormat= 0x280f791e0:bounds.size
stringWithFormat= 0x280f791e0:bounds.size
stringWithFormat= 0x280f791e0:bounds
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= VC:UIEditingOverlayViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= KBLayouts_iPhone.dat
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified--
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified--
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified_0
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= 0x280f1ea80:position
stringWithFormat= 0x280f1ea80:position
stringWithFormat= 0x280f1ea80:position
stringWithFormat= 0x280f1ea80:bounds.origin
stringWithFormat= 0x280f1ea80:bounds.origin
stringWithFormat= 0x280f1ea80:bounds
stringWithFormat= 0x280f1ea80:bounds.size
stringWithFormat= 0x280f1ea80:bounds.size
stringWithFormat= 0x280f1ea80:bounds
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified--
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= 0x280f1eb20:position
stringWithFormat= 0x280f1eb20:position
stringWithFormat= 0x280f1eb20:position
stringWithFormat= 0x280f1eb20:bounds.origin
stringWithFormat= 0x280f1eb20:bounds.origin
stringWithFormat= 0x280f1eb20:bounds
stringWithFormat= 0x280f1eb20:bounds.size
stringWithFormat= 0x280f1eb20:bounds.size
stringWithFormat= 0x280f1eb20:bounds
stringWithFormat= international
stringWithFormat= zh_Hans-Pinyin@sw=Pinyin10-Simplified;hw=Automatic^zh_CN^33284_PortraitChoco_iPhone-Pinyin10-Keyboard_Pinyin-Plane^375
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= Keyboard-default.plist
stringWithFormat= zh-iPhone
stringWithFormat= zh_Hans
stringWithFormat= zh-Pinyin
stringWithFormat= zh-Pinyin-iPhone
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin-iPhone
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans_CN.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh-Pinyin.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans-Pinyin.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh-iPhone.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh-Pinyin-iPhone.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans-Pinyin-iPhone.plist
stringWithFormat= intl
stringWithFormat= intl
stringWithFormat= intl
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= en_US
stringWithFormat= en-US-(null)
stringWithFormat= en-US-(null)-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= en-iPhone
stringWithFormat= en_US
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans.plist
stringWithFormat= /System/Library/TextInput/TextInput_zh.bundle
stringWithFormat= Keyboard-zh_Hans_CN.plist
stringWithFormat= /System/Library/TextInput/TextInput_en.bundle
stringWithFormat= Keyboard-en.plist
stringWithFormat= /System/Library/TextInput/TextInput_en.bundle
stringWithFormat= Keyboard-en_US.plist
stringWithFormat= /System/Library/TextInput/TextInput_en.bundle
stringWithFormat= Keyboard-en-iPhone.plist
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= Alternate-Currency-Sign-1
stringWithFormat= Alternate-Currency-Sign-2
stringWithFormat= Alternate-Currency-Sign-3
stringWithFormat= Alternate-Currency-Sign-4
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh_Hans-Pinyin@sw=Pinyin10-Simplified;hw=Automatic^zh_CN^33284_PortraitChoco_iPhone-Pinyin10-Keyboard_Pinyin-Plane^375
stringWithFormat= zh-Hans-Pinyin
stringWithFormat= zh-Hans-Pinyin-0-zh-Hans-CN-WITH-Defaults
stringWithFormat= zh_Hans-Pinyin@sw=Pinyin10-Simplified;hw=Automatic^zh_CN^33284_PortraitChoco_iPhone-Pinyin10-Keyboard_Pinyin-Plane^375
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= 0x2808d0220:cornerRadius
stringWithFormat= 0x2808d0220:cornerRadius
stringWithFormat= 0x2808d0220:cornerRadius
stringWithFormat= 0x282a0f660:contentsMultiplyColor
stringWithFormat= 0x282a0f660:contentsMultiplyColor
stringWithFormat= 0x280f7cac0:backgroundColor
stringWithFormat= 0x280f7cae0:backgroundColor
stringWithFormat= 0x2808d0220:backgroundColor
stringWithFormat= 0x280f7cf40:cornerRadius
stringWithFormat= 0x280f7cf40:cornerRadius
stringWithFormat= 0x280f7cf40:cornerRadius
stringWithFormat= 0x282a07840:contentsMultiplyColor
stringWithFormat= 0x282a07840:contentsMultiplyColor
stringWithFormat= 0x2808d0260:backgroundColor
stringWithFormat= 0x2808d0280:backgroundColor
stringWithFormat= 0x280f7cf40:backgroundColor
stringWithFormat= 0x280f7d180:cornerRadius
stringWithFormat= 0x280f7d180:cornerRadius
stringWithFormat= 0x280f7d180:cornerRadius
stringWithFormat= 0x282a0f7a0:contentsMultiplyColor
stringWithFormat= 0x282a0f7a0:contentsMultiplyColor
stringWithFormat= 0x280f7d1e0:backgroundColor
stringWithFormat= 0x280f7d260:backgroundColor
stringWithFormat= 0x280f7d180:backgroundColor
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1190583274979_{8, 2, 0, 2}
stringWithFormat= HBCB_1190583274979_{4, 3, 4, 3}
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1190583274979_{8, 2, 0, 2}
stringWithFormat= HBCB_1190583274979_{4, 3, 4, 3}
stringWithFormat= HBCB_1190583274979_{4, 2, 4, 2}
stringWithFormat= HBCB_1190583274979_{8, 2, 0, 2}
stringWithFormat= 0x2808d7160:backgroundColor
stringWithFormat= 0x2808d7160:backgroundColor
stringWithFormat= 0x2808d7160:backgroundColor
stringWithFormat= 0x2808d7160:opacity
stringWithFormat= 0x2808d7160:opacity
stringWithFormat= 0x280f7dcc0:backgroundColor
stringWithFormat= 0x280f7dca0:position
stringWithFormat= 0x280f7dca0:position
stringWithFormat= 0x280f7dca0:position
stringWithFormat= 0x280f7dca0:bounds.origin
stringWithFormat= 0x280f7dca0:bounds.origin
stringWithFormat= 0x280f7dca0:bounds
stringWithFormat= 0x280f7dca0:bounds.size
stringWithFormat= 0x280f7dca0:bounds.size
stringWithFormat= 0x280f7dca0:bounds
stringWithFormat= 0x280f7df20:cornerRadius
stringWithFormat= 0x280f7df20:cornerRadius
stringWithFormat= 0x280f7df20:cornerRadius
stringWithFormat= 0x280f7e0a0:backgroundColor
stringWithFormat= 0x280f7e0a0:backgroundColor
stringWithFormat= 0x280f7e0a0:backgroundColor
stringWithFormat= 0x280f7e0a0:opacity
stringWithFormat= 0x280f7e0a0:opacity
stringWithFormat= 0x280f7e780:position
stringWithFormat= 0x280f7e780:position
stringWithFormat= 0x280f7e780:position
stringWithFormat= 0x280f7e780:bounds.origin
stringWithFormat= 0x280f7e780:bounds.origin
stringWithFormat= 0x280f7e780:bounds
stringWithFormat= 0x280f7e780:bounds.size
stringWithFormat= 0x280f7e780:bounds.size
stringWithFormat= 0x280f7e780:bounds
stringWithFormat= 0x2808d63e0:backgroundColor
stringWithFormat= 0x2808d6e40:position
stringWithFormat= 0x2808d6e40:position
stringWithFormat= 0x2808d6e40:position
stringWithFormat= 0x2808d6e40:bounds.origin
stringWithFormat= 0x2808d6e40:bounds.origin
stringWithFormat= 0x2808d6e40:bounds
stringWithFormat= 0x2808d6e40:bounds.size
stringWithFormat= 0x2808d6e40:bounds.size
stringWithFormat= 0x2808d6e40:bounds
stringWithFormat= 0x2808d6da0:backgroundColor
stringWithFormat= 0x280f7e0a0:opacity
stringWithFormat= 0x2808d6da0:borderColor
stringWithFormat= 0x2808d6da0:borderColor
stringWithFormat= 0x280f7ea20:position
stringWithFormat= 0x280f7ea20:position
stringWithFormat= 0x280f7ea20:position
stringWithFormat= 0x280f7ea20:bounds.origin
stringWithFormat= 0x280f7ea20:bounds.origin
stringWithFormat= 0x280f7ea20:bounds
stringWithFormat= 0x280f7ea20:bounds.size
stringWithFormat= 0x280f7ea20:bounds.size
stringWithFormat= 0x280f7ea20:bounds
stringWithFormat= 0x280f7ea20:backgroundColor
stringWithFormat= 0x280f7dec0:position
stringWithFormat= 0x280f7dec0:position
stringWithFormat= 0x280f7dec0:position
stringWithFormat= 0x280f7dec0:bounds.origin
stringWithFormat= 0x280f7dec0:bounds.origin
stringWithFormat= 0x280f7dec0:bounds
stringWithFormat= 0x280f7dec0:bounds.size
stringWithFormat= 0x280f7dec0:bounds.size
stringWithFormat= 0x280f7dec0:bounds
stringWithFormat= 0x280f7dec0:borderColor
stringWithFormat= 0x280f7dec0:borderColor
stringWithFormat= com.apple.coreui-cache /System/Library/PrivateFrameworks/UIKitCore.framework/Artwork.bundle/Assets.car
stringWithFormat= com.apple.coreui-negativecache /System/Library/PrivateFrameworks/UIKitCore.framework/Artwork.bundle/Assets.car
stringWithFormat= com.apple.coreui-cuicache /System/Library/PrivateFrameworks/UIKitCore.framework/Artwork.bundle/Assets.car
stringWithFormat= 0x280f7f3e0:opacity
stringWithFormat= 0x280f7f3e0:opacity
stringWithFormat= 0x280f7ed80:opacity
stringWithFormat= 0x280f7ed80:opacity
stringWithFormat= 0x2808d6ec0:borderColor
stringWithFormat= 0x2808d6ec0:borderColor
stringWithFormat= 0x280f7dec0:backgroundColor
stringWithFormat= 0x280f7f3e0:contentsMultiplyColor
stringWithFormat= 0x280f7f3e0:contentsMultiplyColor
stringWithFormat= 0x280f7f3e0:opacity
stringWithFormat= 0x280f7f5a0:position
stringWithFormat= 0x280f7f5a0:position
stringWithFormat= 0x280f7f5a0:position
stringWithFormat= 0x280f7f5a0:bounds.origin
stringWithFormat= 0x280f7f5a0:bounds.origin
stringWithFormat= 0x280f7f5a0:bounds
stringWithFormat= 0x280f7f5a0:bounds.size
stringWithFormat= 0x280f7f5a0:bounds.size
stringWithFormat= 0x280f7f5a0:bounds
stringWithFormat= 0x280f7f5a0:backgroundColor
stringWithFormat= 0x280f7ed80:backgroundColor
stringWithFormat= 0x280f7ed60:backgroundColor
stringWithFormat= 0x2808d69c0:position
stringWithFormat= 0x2808d69c0:position
stringWithFormat= 0x2808d69c0:position
stringWithFormat= 0x2808d59c0:position
stringWithFormat= 0x2808d59c0:position
stringWithFormat= 0x2808d59c0:position
stringWithFormat= 0x2808d59c0:bounds.origin
stringWithFormat= 0x2808d59c0:bounds.origin
stringWithFormat= 0x2808d59c0:bounds
stringWithFormat= 0x2808d59c0:bounds.size
stringWithFormat= 0x2808d59c0:bounds.size
stringWithFormat= 0x2808d59c0:bounds
stringWithFormat= 0x2808d5b00:position
stringWithFormat= 0x2808d5b00:position
stringWithFormat= 0x2808d5b00:position
stringWithFormat= 0x2808d5b00:bounds.origin
stringWithFormat= 0x2808d5b00:bounds.origin
stringWithFormat= 0x2808d5b00:bounds
stringWithFormat= 0x2808d5b00:bounds.size
stringWithFormat= 0x2808d5b00:bounds.size
stringWithFormat= 0x2808d5b00:bounds
stringWithFormat= 0x2808d5b00:contentsMultiplyColor
stringWithFormat= 0x2808d5b00:contentsMultiplyColor
stringWithFormat= 0x280f7ed40:borderColor
stringWithFormat= 0x280f7ed40:borderColor
stringWithFormat= 0x2808d6f80:position
stringWithFormat= 0x2808d6f80:position
stringWithFormat= 0x2808d6f80:position
stringWithFormat= 0x2808d6f80:bounds.origin
stringWithFormat= 0x2808d6f80:bounds.origin
stringWithFormat= 0x2808d6f80:bounds
stringWithFormat= 0x2808d6f80:bounds.size
stringWithFormat= 0x2808d6f80:bounds.size
stringWithFormat= 0x2808d6f80:bounds
stringWithFormat= 0x280f7fbc0:position
stringWithFormat= 0x280f7fbc0:position
stringWithFormat= 0x280f7fbc0:position
stringWithFormat= 0x280f7fbc0:bounds.origin
stringWithFormat= 0x280f7fbc0:bounds.origin
stringWithFormat= 0x280f7fbc0:bounds
stringWithFormat= 0x280f7fbc0:bounds.size
stringWithFormat= 0x280f7fbc0:bounds.size
stringWithFormat= 0x280f7fbc0:bounds
stringWithFormat= 0x280f7fbc0:opacity
stringWithFormat= 0x280f7fbc0:opacity
stringWithFormat= 0x280f7fd80:position
stringWithFormat= 0x280f7fd80:position
stringWithFormat= 0x280f7fd80:position
stringWithFormat= 0x280f7fd80:bounds.origin
stringWithFormat= 0x280f7fd80:bounds.origin
stringWithFormat= 0x280f7fd80:bounds
stringWithFormat= 0x280f7fd80:bounds.size
stringWithFormat= 0x280f7fd80:bounds.size
stringWithFormat= 0x280f7fd80:bounds
stringWithFormat= 0x280f7fd80:opacity
stringWithFormat= 0x280f7fd80:opacity
stringWithFormat= 0x2808d6ec0:backgroundColor
stringWithFormat= 0x2808d6f80:backgroundColor
stringWithFormat= 0x2808d7160:opacity
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= 0x2808d7f00:position
stringWithFormat= 0x2808d7f00:position
stringWithFormat= 0x2808d7f00:position
stringWithFormat= 0x2808d7f00:bounds.origin
stringWithFormat= 0x2808d7f00:bounds.origin
stringWithFormat= 0x2808d7f00:bounds
stringWithFormat= 0x2808d7f00:bounds.size
stringWithFormat= 0x2808d7f00:bounds.size
stringWithFormat= 0x2808d7f00:bounds
stringWithFormat= position-2
stringWithFormat= 0x280f7fd80:position
stringWithFormat= 0x280f7fd80:position-2
stringWithFormat= 0x280f7fd80:position
stringWithFormat= bounds.origin-2
stringWithFormat= 0x280f7fd80:bounds.origin
stringWithFormat= 0x280f7fd80:bounds.origin-2
stringWithFormat= 0x280f7fd80:bounds
stringWithFormat= bounds.size-2
stringWithFormat= 0x280f7fd80:bounds.size
stringWithFormat= 0x280f7fd80:bounds.size-2
stringWithFormat= 0x280f7fd80:bounds
stringWithFormat= position-2
stringWithFormat= 0x280f7fbc0:position
stringWithFormat= 0x280f7fbc0:position-2
stringWithFormat= 0x280f7fbc0:position
stringWithFormat= bounds.origin-2
stringWithFormat= 0x280f7fbc0:bounds.origin
stringWithFormat= 0x280f7fbc0:bounds.origin-2
stringWithFormat= 0x280f7fbc0:bounds
stringWithFormat= bounds.size-2
stringWithFormat= 0x280f7fbc0:bounds.size
stringWithFormat= 0x280f7fbc0:bounds.size-2
stringWithFormat= 0x280f7fbc0:bounds
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1230399811394_{8, 2, 0, 2}
stringWithFormat= HBCB_1230399811394_{4, 3, 4, 3}
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1230399811394_{8, 2, 0, 2}
stringWithFormat= HBCB_1230399811394_{4, 3, 4, 3}
stringWithFormat= HBCB_1230399811394_{4, 2, 4, 2}
stringWithFormat= HBCB_1230399811394_{8, 2, 0, 2}
stringWithFormat= position-2
stringWithFormat= 0x2808d6f80:position
stringWithFormat= 0x2808d6f80:position-2
stringWithFormat= 0x2808d6f80:position
stringWithFormat= 0x2808d6ec0:position
stringWithFormat= 0x2808d6ec0:position
stringWithFormat= 0x2808d6ec0:position
stringWithFormat= 0x2808d6ec0:bounds.origin
stringWithFormat= 0x2808d6ec0:bounds.origin
stringWithFormat= 0x2808d6ec0:bounds
stringWithFormat= 0x2808d6ec0:bounds.size
stringWithFormat= 0x2808d6ec0:bounds.size
stringWithFormat= 0x2808d6ec0:bounds
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= frame.origin.x
stringWithFormat= frame.origin.y
stringWithFormat= frame.size.width
stringWithFormat= frame.size.height
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIRemoteKeyboardWindow-0x110930000-2
stringWithFormat= _UIKeyboardWindowScene: 0x10ff9b020: end key window deferring for window hiding: 0x110930000
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= VC:UIInputWindowController
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= application<com.superdev.AMG>
stringWithFormat= [application<com.superdev.AMG>:83519]
stringWithFormat= {{0, 0}, {375, 216}}
stringWithFormat= VC:UICandidateViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= 83519
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= 348-81066-32691
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= effects-audio
stringWithFormat= effects-haptic
stringWithFormat= keyboard-audio
stringWithFormat= keyboard-haptic
stringWithFormat= lock-audio
stringWithFormat= lock-haptic
stringWithFormat= keyboard-audio
stringWithFormat= keyboard-haptic
stringWithFormat= <_UIKeyboardFeedbackGenerator: 0x283e29c30>
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= filters.gaussianBlur.
stringWithFormat= 3
stringWithFormat= 4
stringWithFormat= filters.colorMatrix.
stringWithFormat= 0x2808f58a0:backgroundColor
stringWithFormat= 0x2808f5920:position
stringWithFormat= 0x2808f5920:position
stringWithFormat= 0x2808f5920:position
stringWithFormat= 0x2808f5920:bounds.origin
stringWithFormat= 0x2808f5920:bounds.origin
stringWithFormat= 0x2808f5920:bounds
stringWithFormat= 0x2808f5920:bounds.size
stringWithFormat= 0x2808f5920:bounds.size
stringWithFormat= 0x2808f5920:bounds
stringWithFormat= 0x280f7dc00:position
stringWithFormat= 0x280f7dc00:position
stringWithFormat= 0x280f7dc00:position
stringWithFormat= 0x280f7dc00:bounds.origin
stringWithFormat= 0x280f7dc00:bounds.origin
stringWithFormat= 0x280f7dc00:bounds
stringWithFormat= 0x280f7dc00:bounds.size
stringWithFormat= 0x280f7dc00:bounds.size
stringWithFormat= 0x280f7dc00:bounds
stringWithFormat= 0x2808938a0:backgroundColor
stringWithFormat= 0x2808936c0:position
stringWithFormat= 0x2808936c0:position
stringWithFormat= 0x2808936c0:position
stringWithFormat= 0x2808936c0:bounds.origin
stringWithFormat= 0x2808936c0:bounds.origin
stringWithFormat= 0x2808936c0:bounds
stringWithFormat= 0x2808936c0:bounds.size
stringWithFormat= 0x2808936c0:bounds.size
stringWithFormat= 0x2808936c0:bounds
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.colorMatrix.
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= 0x280892b80:position
stringWithFormat= 0x280892b80:uiFractionalProgress
stringWithFormat= 0x280892b80:position
stringWithFormat= {{0, 0}, {375, 292}}
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= VC:UICompatibilityInputViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= 0x280f1e400:position
stringWithFormat= 0x280f1e400:uiFractionalProgress
stringWithFormat= 0x280f1e400:position
stringWithFormat= 0x280f4dfe0:position
stringWithFormat= 0x280f4dfe0:uiFractionalProgress
stringWithFormat= UIPacingAnimationForAnimatorsKey-2
stringWithFormat= 0x280f4dfe0:position
stringWithFormat= 0x280f4d740:bounds.origin
stringWithFormat= 0x280f4d740:bounds.origin
stringWithFormat= 0x280f4d740:bounds
stringWithFormat= 0x280f4d740:bounds.size
stringWithFormat= 0x280f4d740:bounds.size
stringWithFormat= 0x280f4d740:bounds
stringWithFormat= 0x280f4d740:position
stringWithFormat= 0x280f4d740:position
stringWithFormat= 0x280f4d740:position
stringWithFormat= 0x280f4d760:bounds.origin
stringWithFormat= 0x280f4d760:bounds.origin
stringWithFormat= 0x280f4d760:bounds
stringWithFormat= 0x280f4d760:bounds.size
stringWithFormat= 0x280f4d760:bounds.size
stringWithFormat= 0x280f4d760:bounds
stringWithFormat= 0x280f4d760:position
stringWithFormat= 0x280f4d760:position
stringWithFormat= 0x280f4d760:position
stringWithFormat= 0x280f758a0:position
stringWithFormat= 0x280f758a0:uiFractionalProgress
stringWithFormat= 0x280f758a0:position
stringWithFormat= 0x280f758a0:bounds.origin
stringWithFormat= 0x280f758a0:bounds.origin
stringWithFormat= 0x280f758a0:bounds
stringWithFormat= 0x280f758a0:bounds.size
stringWithFormat= 0x280f758a0:bounds.size
stringWithFormat= 0x280f758a0:bounds
stringWithFormat= 0x280f75860:position
stringWithFormat= 0x280f75860:position
stringWithFormat= 0x280f75860:position
stringWithFormat= 0x280f75860:bounds.origin
stringWithFormat= 0x280f75860:bounds.origin
stringWithFormat= 0x280f75860:bounds
stringWithFormat= 0x280f75860:bounds.size
stringWithFormat= 0x280f75860:bounds.size
stringWithFormat= 0x280f75860:bounds
stringWithFormat= 0x280f1ee80:opacity
stringWithFormat= 0x280f1ee80:opacity
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.colorMatrix.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= 0x28074bab0:filters.gaussianBlur.inputRadius
stringWithFormat= 0x28074bab0:filters.gaussianBlur.inputRadius
stringWithFormat= filters.colorMatrix.
stringWithFormat= 0x28074bab0:filters.colorMatrix.inputColorMatrix
stringWithFormat= 0x28074bab0:filters.colorMatrix.inputColorMatrix
stringWithFormat= 0x28074bab0:scale
stringWithFormat= 0x28074bab0:scale
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.colorMatrix.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= 0x28074d8c0:filters.gaussianBlur.inputRadius
stringWithFormat= 0x28074d8c0:filters.gaussianBlur.inputRadius
stringWithFormat= filters.colorMatrix.
stringWithFormat= 0x28074d8c0:filters.colorMatrix.inputColorMatrix
stringWithFormat= 0x28074d8c0:filters.colorMatrix.inputColorMatrix
stringWithFormat= 0x28074d8c0:scale
stringWithFormat= 0x28074d8c0:scale
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= frame.origin.x
stringWithFormat= frame.origin.y
stringWithFormat= frame.size.width
stringWithFormat= frame.size.height
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= HBCB_1227745375633_{4, 2, 4, 2}
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= HBCB_1227745375633_{4, 2, 4, 2}
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= _AXNotification_SpeakCorrectionsEnabled
stringWithFormat= keyboard.default
stringWithFormat= keyboard.default
stringWithFormat= zh_Hans-Pinyin@sw=Pinyin10-Simplified;hw=Automatic^zh_CN^33284_PortraitChoco_iPhone-Pinyin10-Keyboard_Pinyin-Plane^375
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= {{0, 0}, {375, 292}}
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= HBCB_1227745375633_{4, 2, 4, 2}
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HTProfile.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= HTEPL.HangTracerEnableCustomerMode
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= PLTasking.HangTracerEnableCustomerMode
stringWithFormat= PSI_4_6_4_42
stringWithFormat= PSI_0_6_4_42
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= PDSEHangTracer.HangTracerEnableCustomerMode
stringWithFormat= HBCB_1227745375633_{4, 3, 4, 3}
stringWithFormat= PDSEHTThirdParty.HangTracerEnableCustomerMode
stringWithFormat= HBCB_1227745375633_{4, 2, 4, 2}
stringWithFormat= HBCB_1227745375633_{8, 2, 0, 2}
stringWithFormat= PDSEHTRateOnly.HangTracerEnableCustomerMode
stringWithFormat= PDSESentry.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= HTProfile.HangTracerEnableCustomerMode
stringWithFormat= MediaPlayer.framework
stringWithFormat= UIWindowScene: 0x10ff08e30: Window scene became target of keyboard environment
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= HTEPL.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PLTasking.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHangTracer.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHTThirdParty.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHTRateOnly.HangTracerEnableCustomerMode
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSESentry.HangTracerEnableCustomerMode
stringWithFormat= HTProfile.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= HTEPL.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= PLTasking.HangTracerEnabled
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHangTracer.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHTThirdParty.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSEHTRateOnly.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= PDSESentry.HangTracerEnabled
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= VC:UICompatibilityInputViewController
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.TextInput.preferences
stringWithFormat= __NSXPCInterfaceProxy_TIPreferencesControllerActions
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementInitialPosition>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= en YES / int YES
stringWithFormat= en YES / int YES
stringWithFormat= en YES / int YES
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.pasteboard.pasted
stringWithFormat= __NSXPCInterfaceProxy_PBClientToServerProtocol
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= 0x10ff9fc80-2
stringWithFormat= 0x10ffa7a50-2
stringWithFormat= 0x10ffadfa0-2
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.colorMatrix.
stringWithFormat= filters.gaussianBlur.
stringWithFormat= filters.colorMatrix.
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified--
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= iPhone-PortraitChoco-Pinyin10-Simplified--
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UITouchesEvent: 0x283c29e00> timestamp: 387947 touches: {(
    <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
)}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= filters.vibrantColorMatrix.
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Began tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {78, 249} previous location in window: {78, 249} location in view: {25.5, 12.5} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Ended tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {72.5, 247.5} previous location in window: {78, 249} location in view: {20, 11} previous location in view: {25.5, 12.5}
stringWithFormat= <UITouchesEvent: 0x283c29e00> timestamp: 387947 touches: {(
    <UITouch: 0x10fe27150> phase: Ended tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {72.5, 247.5} previous location in window: {78, 249} location in view: {20, 11} previous location in view: {25.5, 12.5}
)}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Ended tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {72.5, 247.5} previous location in window: {78, 249} location in view: {20, 11} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Ended tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {72.5, 247.5} previous location in window: {78, 249} location in view: {20, 11} previous location in view: {25.5, 12.5}
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= UIDictationController logCorrectionStatisticsForDelegate:reason:
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= en_US
stringWithFormat= emoji
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= frame.origin.x
stringWithFormat= frame.origin.y
stringWithFormat= frame.size.width
stringWithFormat= frame.size.height
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= UIVariableDelayLoupeGesture: 0x10ff7bf00
stringWithFormat= <UIVariableDelayLoupeGesture: 0x10ff7bf00 (UITextInteractionNameInteractiveLoupe)>
stringWithFormat= UITapAndAHalfRecognizer: 0x10ff7c0e0
stringWithFormat= <UITapAndAHalfRecognizer: 0x10ff7c0e0 (UITextInteractionNameTapAndAHalf)>
stringWithFormat= UITextMultiTapRecognizer: 0x10ff7c220
stringWithFormat= <UITextMultiTapRecognizer: 0x10ff7c220 (UITextInteractionNameSingleTap)>
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= 83519
stringWithFormat= {{0, 0}, {0, 0}}
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.width
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOnScreen>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.width
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.horizontal
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.vertical
stringWithFormat= UIInputViewSetPlacement_GenericApplicator<UIInputViewSetPlacementOffScreenDown>.width
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1a4b0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1a4b0>
stringWithFormat= UIKBProductivitySingleTapGesture: 0x10fe71890
stringWithFormat= <UIKBProductivitySingleTapGesture: 0x10fe71890 (kbProductivity.threeFingerSingleTap)>
stringWithFormat= UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0
stringWithFormat= <UIKBProductivityLongPressGestureRecognizer: 0x10fe0c5a0 (kbProductivity.threeFingerLongPress)>
stringWithFormat= _UISystemGestureGateGestureRecognizer: 0x10ff1b1f0
stringWithFormat= <_UISystemGestureGateGestureRecognizer: 0x10ff1b1f0>
stringWithFormat= UIKBProductivityPinchGestureRecognizer: 0x10fe8b260
stringWithFormat= <UIKBProductivityPinchGestureRecognizer: 0x10fe8b260 (kbProductivity.threeFingerPinch)>
stringWithFormat= UIUndoGestureObserver: 0x28362d680
stringWithFormat= <UIUndoGestureObserver: 0x28362d680 (undointeraction.dismissHUD)>
stringWithFormat= UIKBProductivityPanGestureRecognizer: 0x10fe6df20
stringWithFormat= <UIKBProductivityPanGestureRecognizer: 0x10fe6df20 (kbProductivity.threeFingerPan)>
stringWithFormat= UIKBProductivityDoubleTapGesture: 0x10fe858f0
stringWithFormat= <UIKBProductivityDoubleTapGesture: 0x10fe858f0 (undointeraction.threeFingerDoubleTap)>
stringWithFormat= UILongPressGestureRecognizer: 0x10fe39320
stringWithFormat= <UILongPressGestureRecognizer: 0x10fe39320 (UIInterfaceAction.selection)>
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>
stringWithFormat= <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>>
stringWithFormat= <UITouch: 0x10fe27150> phase: Ended tap count: 1 force: 0.000 window: <UIWindow: 0x10ff1a8d0; frame = (0 0; 375 667); autoresize = W+H; gestureRecognizers = <NSArray: 0x280754510>; layer = <UIWindowLayer: 0x280755bf0>> view: <_UIAlertControllerActionView: 0x10fe3d020; frame = (0 0; 135 44); Action = <UIAlertAction: 0x283b29e60 Title = "退出" Descriptive = "(null)" Image = 0x0>> location in window: {72.5, 247.5} previous location in window: {78, 249} location in view: {20, 11} previous location in view: {25.5, 12.5}
stringWithFormat= <UIWindow: 0x10ff1a8d0; 
stringWithFormat= autoresize = W+H; 
stringWithFormat= UITextRangeAdjustmentGestureRecognizer: 0x10fe788b0
stringWithFormat= <UITextRangeAdjustmentGestureRecognizer: 0x10fe788b0 (UITextRangeAdjustmentGestureRecognizer)>
stringWithFormat= en YES / int YES
stringWithFormat= 0x280f4dfe0:opacity
stringWithFormat= 0x280f4dfe0:uiFractionalProgress
stringWithFormat= 0x280f71e80:opacity
stringWithFormat= 0x280f71e80:opacity
stringWithFormat= 0x282a20e60:contents
stringWithFormat= <_UIKeyboardFeedbackGenerator: 0x283e29c30>
stringWithFormat= keyboard.default
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= <_UIFeedbackSystemSoundEngine: 0x28221d1f0>
stringWithFormat= 0
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithUTF8String= 1785682448
stringWithFormat= 1785682448
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= com.apple.NSXPCConnection.m-user.com.apple.TextInput
stringWithFormat= __NSXPCInterfaceProxy_TIKeyboardInputManagerToImplProtocol
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= zh_Hans-Pinyin
stringWithFormat= <_UIAlertControllerView 0x10ff54400>
stringWithFormat= <_UIAlertControllerView: 0x10ff54400; 
stringWithFormat= BSXPCServiceConnectionProxy<FBSWorkspaceServiceClientInterface>
stringWithFormat= 83519
