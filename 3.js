var targetModule = "AMG";
var targetOffset = 0x6014ec

function attachToTargetModule() {
    Process.enumerateModules({
        onMatch: function(module) {
            if (module.name === targetModule) {
                console.log("[*] 模块名称: " + module.name);
                console.log("[*] 模块基址: " + module.base);

                var targetAddress = module.base.add(targetOffset);
                console.log("[*] Hook 地址: " + targetAddress);

                Interceptor.attach(targetAddress, {
                    onEnter: function(args) {
                        console.log("[+]函数被调用");

                        // 打印传入参数
                        console.log("[+] 参数个数: " + args.length);
                        for (var i = 0; i < args.length && i < 8; i++) {
                            console.log("[+] 参数[" + i + "] (十六进制): 0x" + args[i].toString(16));
                            console.log("[+] 参数[" + i + "] (十进制): " + args[i].toString(10));
                            console.log("[+] 参数[" + i + "] (指针): " + args[i]);

                            // 尝试读取参数指向的内容
                            try {
                                if (!args[i].isNull()) {
                                    var pointedValue = args[i].readPointer();
                                    console.log("[+] 参数[" + i + "] 指向的值: " + pointedValue);

                                    // 尝试读取字符串
                                    try {
                                        var str = args[i].readCString();
                                        if (str && str.length > 0 && str.length < 256) {
                                            console.log("[+] 参数[" + i + "] 字符串内容: \"" + str + "\"");
                                        }
                                    } catch (e) {
                                        // 不是字符串，忽略
                                    }
                                }
                            } catch (e) {
                                console.log("[+] 参数[" + i + "] 不是有效指针");
                            }
                            console.log("---");
                        }

                        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
                            .map(addr => {
                                const m = Process.findModuleByAddress(addr);
                                if (m) {
                                    const offset = addr.sub(m.base);
                                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                                }
                                return `0x${addr.toString(16)}`;
                            })
                            .join('\n')
                        );
                        console.log("[+] ---------------------------------------------------------------");
                    },

                    onLeave: function(retval) {
                        console.log("[+] 函数返回");
                        console.log("[+] 返回值 (十六进制): 0x" + retval.toString(16));
                        console.log("[+] 返回值 (十进制): " + retval.toString(10));
                        console.log("[+] 返回值 (指针): " + retval);

                        // 如果返回值是指针，尝试读取指向的内容
                        try {
                            if (!retval.isNull()) {
                                var pointedValue = retval.readPointer();
                                console.log("[+] 指向的值: " + pointedValue);
                            }
                        } catch (e) {
                            console.log("[+] 返回值不是有效指针或无法读取");
                        }

                        console.log("[+] ===============================================================");
                    }
                });
            }
        },
        onComplete: function() {
            console.log("[*] 模块枚举完成");
        }
    });
}

setTimeout(attachToTargetModule, 100);