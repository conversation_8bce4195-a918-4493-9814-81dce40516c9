var targetModule = "AMG";
var targetOffset = 0x6014ec

function attachToTargetModule() {
    Process.enumerateModules({
        onMatch: function(module) {
            if (module.name === targetModule) {
                console.log("[*] 模块名称: " + module.name);
                console.log("[*] 模块基址: " + module.base);

                var targetAddress = module.base.add(targetOffset);
                console.log("[*] Hook 地址: " + targetAddress);

                Interceptor.attach(targetAddress, {
                    onEnter: function(args) {
                        console.log("[+]函数被调用");
                        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
                            .map(addr => {
                                const m = Process.findModuleByAddress(addr);
                                if (m) {
                                    const offset = addr.sub(m.base);
                                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                                }
                                return `0x${addr.toString(16)}`;
                            })
                            .join('\n')
                        );
                        console.log("[+] ---------------------------------------------------------------");

                        // 读取原始字符串
                        var originalString = this.context.x1.readCString();
                        console.log("[+] 原始字符串: " + originalString);

                        // 设置要写入的固定值
                        var newString = "9999999999";  // 你可以修改这个值

                        // 写入新的字符串到x1指向的内存地址
                        try {
                            this.context.x1.writeUtf8String(newString);
                            console.log("[+] 已修改为: " + newString);

                            // 验证修改是否成功
                            var modifiedString = this.context.x1.readCString();
                            console.log("[+] 验证修改后: " + modifiedString);
                        } catch (e) {
                            console.log("[!] 写入失败: " + e.message);

                            // 如果直接写入失败，尝试分配新内存
                            try {
                                var newMemory = Memory.allocUtf8String(newString);
                                this.context.x1 = newMemory;
                                console.log("[+] 已分配新内存并修改x1寄存器: " + newString);
                                console.log("[+] 新内存地址: " + newMemory);
                            } catch (e2) {
                                console.log("[!] 分配新内存也失败: " + e2.message);
                            }
                        }


                    },

                    onLeave: function(retval) {
                       
                    }
                });
            }
        },
        onComplete: function() {
            console.log("[*] 模块枚举完成");
        }
    });
}

setTimeout(attachToTargetModule, 100);