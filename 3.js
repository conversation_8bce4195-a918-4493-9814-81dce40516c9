var targetModule = "AMG";
var targetOffset = 0x6014ec

function attachToTargetModule() {
    Process.enumerateModules({
        onMatch: function(module) {
            if (module.name === targetModule) {
                console.log("[*] 模块名称: " + module.name);
                console.log("[*] 模块基址: " + module.base);

                var targetAddress = module.base.add(targetOffset);
                console.log("[*] Hook 地址: " + targetAddress);

                Interceptor.attach(targetAddress, {
                    onEnter: function(args) {
                        console.log("[+]函数被调用");
                        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
                            .map(addr => {
                                const m = Process.findModuleByAddress(addr);
                                if (m) {
                                    const offset = addr.sub(m.base);
                                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                                }
                                return `0x${addr.toString(16)}`;
                            })
                            .join('\n')
                        );
                        console.log("[+] ---------------------------------------------------------------");




                    },

                    onLeave: function(retval) {
                        console.log("[+] 函数返回");
                        console.log("[+] 返回值 (十六进制): 0x" + retval.toString(16));
                        console.log("[+] 返回值 (十进制): " + retval.toString(10));
                        console.log("[+] 返回值 (指针): " + retval);

                        // 如果返回值是指针，尝试读取指向的内容
                        try {
                            if (!retval.isNull()) {
                                var pointedValue = retval.readPointer();
                                console.log("[+] 指向的值: " + pointedValue);
                            }
                        } catch (e) {
                            console.log("[+] 返回值不是有效指针或无法读取");
                        }

                        console.log("[+] ===============================================================");
                    }
                });
            }
        },
        onComplete: function() {
            console.log("[*] 模块枚举完成");
        }
    });
}

setTimeout(attachToTargetModule, 100);